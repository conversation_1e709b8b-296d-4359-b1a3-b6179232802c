/**
 * WCAG Scan Results Overview Component
 * Displays high-level scan results and metrics
 */

'use client';

import React from 'react';
import {
  CheckCircle,
  AlertTriangle,
  XCircle,
  BarChart3,
  FileText,
  FileJson,
  FileSpreadsheet,
} from 'lucide-react';
import { WcagScanResult, RiskLevel } from '../../types/wcag';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { Alert } from '../ui/alert';
import { Button } from '../ui/button';

interface WcagScanOverviewProps {
  scanResult: WcagScanResult;
  onExport?: () => void; // Changed to simple callback to open export dialog
  loading?: boolean;
}

const WcagScanOverview: React.FC<WcagScanOverviewProps> = ({ scanResult, onExport, loading }) => {
  /**
   * Get risk level color
   */
  const getRiskColor = (
    riskLevel: RiskLevel,
  ): 'default' | 'secondary' | 'destructive' | 'outline' => {
    switch (riskLevel) {
      case 'low':
        return 'secondary';
      case 'medium':
        return 'outline';
      case 'high':
        return 'destructive';
      case 'critical':
        return 'destructive';
      default:
        return 'default';
    }
  };

  /**
   * Format duration
   */
  const formatDuration = (milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    }
    return `${remainingSeconds}s`;
  };

  /**
   * Calculate automation percentage
   */
  const automationPercentage = Math.round(scanResult.summary.automationRate * 100);

  return (
    <div className="space-y-6">
      {/* Status Alert */}
      {scanResult.status === 'completed' && (
        <Alert variant={scanResult.riskLevel === 'low' ? 'default' : 'destructive'}>
          <CheckCircle className="h-4 w-4" />
          <div>
            <h4 className="font-semibold">
              Scan Completed - {scanResult.levelAchieved} Level Achieved
            </h4>
            <p className="text-sm">
              {' '}
              Overall Score: {scanResult.overallScore}/100 | Risk Level:{' '}
              {scanResult.riskLevel.toUpperCase()}
            </p>
          </div>
        </Alert>
      )}

      {scanResult.status === 'failed' && (
        <Alert variant="destructive">
          <XCircle className="h-4 w-4" />
          <div>
            <h4 className="font-semibold">Scan Failed</h4>
            <p className="text-sm">
              The WCAG compliance scan could not be completed. Please try again.
            </p>
          </div>
        </Alert>
      )}

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* Overall Score */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Overall Score</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center justify-center space-y-2">
              <div className="text-4xl font-bold">{scanResult.overallScore}</div>
              <div className="text-sm text-muted-foreground">out of 100</div>
              <div className="w-full">
                <Progress value={scanResult.overallScore} className="h-2" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Risk Level */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Risk Level</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center justify-center space-y-2">
              <Badge
                variant={getRiskColor(scanResult.riskLevel)}
                className="text-lg px-3 py-1 mb-2"
              >
                {scanResult.riskLevel.toUpperCase()}
              </Badge>
              <div className="text-sm text-muted-foreground">Based on WCAG compliance issues</div>
            </div>
          </CardContent>
        </Card>

        {/* Automation Rate */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Automation Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center justify-center space-y-2">
              <div className="text-4xl font-bold">{automationPercentage}%</div>
              <div className="text-sm text-muted-foreground">of checks automated</div>
              <div className="w-full">
                <Progress value={automationPercentage} className="h-2" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Compliance Summary */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Compliance Summary
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm flex items-center gap-1">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  Passed Checks
                </span>
                <span className="text-sm font-medium text-green-600">
                  {scanResult.summary.passedAutomatedChecks}
                </span>
              </div>
              <Progress
                value={
                  (scanResult.summary.passedAutomatedChecks /
                    scanResult.summary.totalAutomatedChecks) *
                  100
                }
                className="h-2 bg-gray-200 dark:bg-gray-700"
              />
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm flex items-center gap-1">
                  <XCircle className="h-4 w-4 text-red-600" />
                  Failed Checks
                </span>
                <span className="text-sm font-medium text-red-600">
                  {scanResult.summary.failedAutomatedChecks}
                </span>
              </div>
              <Progress
                value={
                  (scanResult.summary.failedAutomatedChecks /
                    scanResult.summary.totalAutomatedChecks) *
                  100
                }
                className="h-2 bg-gray-200 dark:bg-gray-700"
              />
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm flex items-center gap-1">
                  <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  Manual Review Items
                </span>
                <span className="text-sm font-medium text-blue-600">
                  {scanResult.summary.manualReviewItems}
                </span>
              </div>
              <p className="text-xs text-muted-foreground">Requires manual review</p>
            </div>

            <div className="pt-2 border-t">
              <p className="text-sm text-muted-foreground">
                Total Automated Checks: {scanResult.summary.totalAutomatedChecks}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Category Scores */}
        <Card>
          <CardHeader>
            <CardTitle>Category Scores</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {Object.entries(scanResult.summary.categoryScores).map(([category, score]) => {
              const numericScore = Number(score);
              return (
                <div key={category} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm capitalize">{category}</span>
                    <span className="text-sm font-medium">{numericScore}/100</span>
                  </div>
                  <Progress
                    value={numericScore}
                    className={`h-2 ${
                      numericScore >= 80
                        ? 'text-green-600'
                        : numericScore >= 60
                          ? 'text-yellow-600'
                          : 'text-red-600'
                    }`}
                  />
                </div>
              );
            })}
          </CardContent>
        </Card>
      </div>

      {/* Scan Metadata */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row justify-between items-start gap-4">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Scan Details</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                <strong>URL:</strong> {scanResult.targetUrl}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                <strong>Scan Date:</strong>{' '}
                {scanResult.scanTimestamp
                  ? new Date(scanResult.scanTimestamp).toLocaleDateString()
                  : 'N/A'}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                <strong>Duration:</strong>{' '}
                {scanResult.metadata?.duration
                  ? formatDuration(scanResult.metadata.duration)
                  : 'N/A'}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                <strong>Scan ID:</strong> {scanResult.scanId}
              </p>
            </div>

            {onExport && (
              <div className="flex flex-col gap-2">
                <h3 className="text-lg font-semibold">Export Results</h3>
                <div className="flex flex-wrap gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onExport('pdf')}
                    disabled={loading}
                    className="flex items-center gap-1"
                  >
                    <FileText className="h-4 w-4" />
                    PDF
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onExport('csv')}
                    disabled={loading}
                    className="flex items-center gap-1"
                  >
                    <FileSpreadsheet className="h-4 w-4" />
                    CSV
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onExport('json')}
                    disabled={loading}
                    className="flex items-center gap-1"
                  >
                    <FileJson className="h-4 w-4" />
                    JSON
                  </Button>
                </div>
                {loading && <p className="text-sm text-gray-500">Generating export...</p>}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default WcagScanOverview;
